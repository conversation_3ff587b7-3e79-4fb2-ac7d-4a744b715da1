# 产销配比模型前后端接口联调文档

## 概述

本文档记录了产销配比模型前端页面与后端 `maxOutput` 接口的联调工作完成情况。

## 完成的工作

### 1. API函数完善
- **文件**: `src/api/production/prodSales.js`
- **功能**: 完善了 `maxOutput` 函数，添加了详细的参数文档和验证逻辑
- **特性**:
  - 参数类型验证
  - 必需字段检查
  - 数值类型转换
  - 错误处理机制

### 2. 前端页面改进
- **文件**: `src/views/production/prodSales/index.vue`
- **改进内容**:
  - 修正气田名称（唐城 → 崖城）
  - 添加利润目标字段
  - 所有数值输入框添加 `type="number"` 属性
  - 实现完整的数据收集和格式化功能

### 3. 数据映射关系

#### 前端表单字段 → 后端API参数

**膨胀系数参数**:
- `salesChannels[0].expansionCoefficient` → `f1` (香港中电)
- `salesChannels[1].expansionCoefficient` → `f2` (气电南山)
- `salesChannels[2].expansionCoefficient` → `f3` (气电广东)

**价格参数**:
- `salesChannels[0].price` → `p1` (香港中电)
- `salesChannels[1].price` → `p2` (气电南山)
- `salesChannels[2].price` → `p3` (气电广东)

**接收量/供应量参数**:
- `supplyStrategy[0].maxReceive` → `m1Max` (香港中电最大接收量)
- `supplyStrategy[0].minSupply` → `m1Min` (香港中电最小供应量)
- `supplyStrategy[1].maxReceive` → `m2Max` (气电南山最大接收量)
- `supplyStrategy[1].minSupply` → `m2Min` (气电南山最小供应量)
- `supplyStrategy[2].maxReceive` → `m3Max` (气电广东最大接收量)
- `supplyStrategy[2].minSupply` → `m3Min` (气电广东最小供应量)

**气田产量参数**:
- `fieldConstraints[0].maxProduction` → `maMax` (崖城13-1最高产量)
- `fieldConstraints[0].minProduction` → `maMin` (崖城13-1最低产量)
- `fieldConstraints[1].maxProduction` → `mbMax` (崖城13-10最高产量)
- `fieldConstraints[1].minProduction` → `mbMin` (崖城13-10最低产量)
- `fieldConstraints[2].maxProduction` → `mcMax` (陵水17-2最高产量)
- `fieldConstraints[2].minProduction` → `mcMin` (陵水17-2最低产量)
- `fieldConstraints[3].maxProduction` → `mdMax` (陵水25-1最高产量)
- `fieldConstraints[3].minProduction` → `mdMin` (陵水25-1最低产量)

**其他参数**:
- `form.productionTarget` → `ms` (计划产量)
- `form.profitTarget` → `ss` (利润目标)
- `form.productionUnit` → `unit` (产量单位)

### 4. 功能特性

#### 数据验证
- 必需字段检查
- 数值类型验证
- 逻辑关系验证（最小值不大于最大值）

#### 用户体验
- 加载状态显示
- 详细的错误提示
- 表单重置功能
- 实时编译和热重载

#### 错误处理
- API调用错误处理
- 网络错误处理
- 参数验证错误处理
- 用户友好的错误消息

## 测试方法

### 1. 手动测试
1. 访问 `http://localhost:33520/fbpApp/`
2. 导航到产销配比模型页面
3. 填写所有必需字段
4. 点击"开始测算"按钮
5. 观察API调用和响应处理

### 2. 数据格式验证
使用 `src/utils/testProdSalesData.js` 中的测试数据和验证函数：
```javascript
import { testFormData, expectedApiData, validateDataFormat } from '@/utils/testProdSalesData';

// 在浏览器控制台中测试
const result = validateDataFormat(expectedApiData);
console.log(result);
```

## 技术要点

### API请求格式
```javascript
{
  "f1": 1.05, "f2": 1.08, "f3": 1.10,
  "p1": 2.5, "p2": 2.8, "p3": 3.0,
  "m1Max": 500, "m1Min": 100,
  "m2Max": 600, "m2Min": 150,
  "m3Max": 700, "m3Min": 200,
  "maMax": 300, "maMin": 50,
  "mbMax": 400, "mbMin": 80,
  "mcMax": 500, "mcMin": 100,
  "mdMax": 600, "mdMin": 120,
  "ms": 1000, "ss": 500000,
  "unit": "万立方米/天"
}
```

### 响应处理
- 成功响应：显示测算结果
- 错误响应：显示具体错误信息
- 网络错误：显示网络连接提示

## 部署说明

1. 确保后端API服务正常运行
2. 检查 `src/api/config.ts` 中的API基础URL配置
3. 运行 `npm run serve:client` 启动开发服务器
4. 访问 `http://localhost:33520/fbpApp/` 进行测试

## 注意事项

1. 所有数值字段都会自动转换为 `number` 类型
2. 空字符串和 `null` 值会被视为缺失字段
3. API调用失败时不会切换到结果页面
4. 表单重置会清空所有输入数据

## 后续优化建议

1. 添加字段级别的实时验证
2. 实现数据持久化（本地存储）
3. 添加导入/导出功能
4. 优化错误提示的用户体验
5. 添加单元测试和集成测试
