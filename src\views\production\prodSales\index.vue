<template>
  <div class="production-sales-model">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <h1>产销配比模型</h1>
        <div style="float: right;margin-top: -30px;">
          选择模型：
          <el-select v-model="modelType" placeholder="请选择">
            <el-option label="产销配比模型1" value="1"></el-option>
            <el-option label="产销配比模型2" value="2"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 基础参数设定 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>基础参数设定</h2>
        </div>
        <el-form :model="form" :inline="true" label-position="left">
          <el-form-item label="产量单位：">
            <el-input
              v-model="form.productionUnit"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="计划产量目标：">
            <el-input
              v-model="form.productionTarget"
              placeholder="请输入"
              type="number"
            ></el-input>
          </el-form-item>
          <el-form-item label="利润目标：">
            <el-input
              v-model="form.profitTarget"
              placeholder="请输入"
              type="number"
            ></el-input>
          </el-form-item>
        </el-form>
      </el-card>

      <div class="section-box">
        <!-- 终端销售渠道及价格 -->
        <el-card shadow="hover" class="section-card card-left">
          <div slot="header">
            <h2>终端销售渠道及价格</h2>
          </div>
          <el-table :data="salesChannels" border style="width: 100%">
            <el-table-column
              type="index"
              label="序号"
              width="100"
            ></el-table-column>
            <el-table-column
              prop="channel"
              label="渠道"
              width="180"
            ></el-table-column>
            <el-table-column label="价格">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.price"
                  placeholder="请输入"
                  type="number"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column label="膨胀系数">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.expansionCoefficient"
                  placeholder="请输入"
                  type="number"
                ></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 渠道配置 -->
        <el-card shadow="hover" class="section-card card-right">
          <div slot="header">
            <h2>渠道配置</h2>
          </div>
          <ConstraintConfig />
        </el-card>
      </div>

      <!-- 约束条件配置 -->
      <el-card shadow="hover" class="section-card">
        <div slot="header">
          <h2>约束条件配置</h2>
        </div>

        <div class="section-box">
          <div class="card-left">
            <h3>渠道供应策略</h3>
            <el-table :data="supplyStrategy" border style="width: 100%">
              <el-table-column
                type="index"
                label="序号"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="channel"
                label="渠道"
                width="180"
              ></el-table-column>
              <el-table-column label="最小供应量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.minSupply"
                    placeholder="请输入"
                    type="number"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="最大接收量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.maxReceive"
                    placeholder="请输入"
                    type="number"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="card-right">
            <h3>气田产量约束</h3>
            <el-table :data="fieldConstraints" border style="width: 100%">
              <el-table-column
                type="index"
                label="序号"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="field"
                label="气田"
                width="180"
              ></el-table-column>
              <el-table-column label="气田最低产量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.minProduction"
                    placeholder="请输入"
                    type="number"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="气田最高产量">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.maxProduction"
                    placeholder="请输入"
                    type="number"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <div class="action-buttons" v-if="!isCalculating">
        <el-button
          type="primary"
          size="large"
          @click="startCalculation"
          :loading="isLoading"
          :disabled="isLoading"
        >
          {{ isLoading ? '测算中...' : '开始测算' }}
        </el-button>
      </div>

      <el-card shadow="hover" class="section-card" v-else>
        <div slot="header" class="card-header">
          <h2>测算结果</h2>
          <el-button type="primary" @click="resetCalculation">重置</el-button>
        </div>
        <div class="section-box">
          <div class="card-left">
            <el-table
              :data="resultData"
              border
              style="width: 100%"
              show-summary
            >
              <el-table-column
                v-for="item in columns"
                :key="item.channel"
                :prop="item.prop"
                :label="item.label"
              />
            </el-table>
          </div>

          <div class="card-right">
            <div class="result-title">
              <div class="result-title-text">最大销售额</div>
              <div class="result-title-formula">
                Z=P1X31 +P2(X32+X42)+P3(X13+X23+X33)
              </div>
            </div>
            <div class="chart-box">
              <SalesVolume></SalesVolume>
            </div>
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script>
import SalesVolume from "./salesVolume.vue";
import ConstraintConfig from "./ConstraintConfig.vue";
import { maxOutput } from "@/api/production/prodSales";

export default {
  name: "prodSales",
  components: {
    SalesVolume,
    ConstraintConfig,
  },
  mounted() {
    window.document.documentElement.setAttribute("data-theme", "tint");
  },
  data() {
    return {
      modelType:"",
      isCalculating: false,
      isLoading: false,
      form: {
        productionUnit: "",
        productionTarget: "",
        profitTarget: "",
      },
      salesChannels: [
        { id: 1, channel: "香港中电", price: "", expansionCoefficient: "" },
        { id: 2, channel: "气电南山", price: "", expansionCoefficient: "" },
        { id: 3, channel: "气电广东", price: "", expansionCoefficient: "" },
      ],
      channels: ["崖城13-1", "崖城13-10", "陵水17-2", "陵水25-1"],
      selectedChannels: [],
      demandCenters: ["气电南山", "气电广东"],
      selectedDemandCenters: [],
      supplyStrategy: [
        { id: 1, channel: "香港中电", minSupply: "", maxReceive: "" },
        { id: 2, channel: "气电南山", minSupply: "", maxReceive: "" },
        { id: 3, channel: "气电广东", minSupply: "", maxReceive: "" },
      ],
      fieldConstraints: [
        { id: 1, field: "崖城13-1", minProduction: "", maxProduction: "" },
        { id: 2, field: "崖城13-10", minProduction: "", maxProduction: "" },
        { id: 3, field: "陵水17-2", minProduction: "", maxProduction: "" },
        { id: 4, field: "陵水25-1", minProduction: "", maxProduction: "" },
      ],
      columns: [
        { label: "", prop: "channel" },
        { label: "崖城13-1", prop: "yancheng13_1" },
        { label: "崖城13-10", prop: "yancheng13_10" },
        { label: "陵水17-2", prop: "lingshui17_2" },
        { label: "陵水25-1", prop: "lingshui25_1" },
        { label: "总计", prop: "totalSales" },
      ],
      resultData: [
        {
          channel: "香港中电",
          yancheng13_1: "154",
          yancheng13_10: "256",
          lingshui17_2: "369",
          lingshui25_1: "485",
          totalSales: "1264",
        },
        {
          channel: "气电南山",
          yancheng13_1: "123",
          yancheng13_10: "456",
          lingshui17_2: "789",
          lingshui25_1: "321",
          totalSales: "1689",
        },
        {
          channel: "气电广东",
          yancheng13_1: "654",
          yancheng13_10: "987",
          lingshui17_2: "321",
          lingshui25_1: "654",
          totalSales: "2616",
        },
      ],
    };
  },
  methods: {
    /**
     * 收集并格式化表单数据为API所需格式
     */
    collectFormData() {
      // 验证基础参数
      if (!this.form.productionUnit || !this.form.productionTarget || !this.form.profitTarget) {
        this.$message.error("请填写完整的基础参数");
        return null;
      }

      // 验证销售渠道数据
      for (let i = 0; i < this.salesChannels.length; i++) {
        const channel = this.salesChannels[i];
        if (!channel.price || !channel.expansionCoefficient) {
          this.$message.error(`请填写完整的${channel.channel}价格和膨胀系数`);
          return null;
        }
      }

      // 验证供应策略数据
      for (let i = 0; i < this.supplyStrategy.length; i++) {
        const strategy = this.supplyStrategy[i];
        if (!strategy.minSupply || !strategy.maxReceive) {
          this.$message.error(`请填写完整的${strategy.channel}供应策略`);
          return null;
        }
      }

      // 验证气田约束数据
      for (let i = 0; i < this.fieldConstraints.length; i++) {
        const field = this.fieldConstraints[i];
        if (!field.minProduction || !field.maxProduction) {
          this.$message.error(`请填写完整的${field.field}产量约束`);
          return null;
        }
      }

      // 格式化数据
      const apiData = {
        // 膨胀系数参数
        f1: Number(this.salesChannels[0].expansionCoefficient), // 香港中电
        f2: Number(this.salesChannels[1].expansionCoefficient), // 气电南山
        f3: Number(this.salesChannels[2].expansionCoefficient), // 气电广东

        // 价格参数
        p1: Number(this.salesChannels[0].price), // 香港中电
        p2: Number(this.salesChannels[1].price), // 气电南山
        p3: Number(this.salesChannels[2].price), // 气电广东

        // 接收量/供应量参数
        m1Max: Number(this.supplyStrategy[0].maxReceive), // 香港中电最大接收量
        m1Min: Number(this.supplyStrategy[0].minSupply),  // 香港中电最小供应量
        m2Max: Number(this.supplyStrategy[1].maxReceive), // 气电南山最大接收量
        m2Min: Number(this.supplyStrategy[1].minSupply),  // 气电南山最小供应量
        m3Max: Number(this.supplyStrategy[2].maxReceive), // 气电广东最大接收量
        m3Min: Number(this.supplyStrategy[2].minSupply),  // 气电广东最小供应量

        // 气田产量参数
        maMax: Number(this.fieldConstraints[0].maxProduction), // 崖城13-1最高产量
        maMin: Number(this.fieldConstraints[0].minProduction), // 崖城13-1最低产量
        mbMax: Number(this.fieldConstraints[1].maxProduction), // 崖城13-10最高产量
        mbMin: Number(this.fieldConstraints[1].minProduction), // 崖城13-10最低产量
        mcMax: Number(this.fieldConstraints[2].maxProduction), // 陵水17-2最高产量
        mcMin: Number(this.fieldConstraints[2].minProduction), // 陵水17-2最低产量
        mdMax: Number(this.fieldConstraints[3].maxProduction), // 陵水25-1最高产量
        mdMin: Number(this.fieldConstraints[3].minProduction), // 陵水25-1最低产量

        // 其他参数
        ms: Number(this.form.productionTarget), // 计划产量
        ss: Number(this.form.profitTarget),     // 利润目标
        unit: this.form.productionUnit          // 产量单位
      };

      return apiData;
    },

    async startCalculation() {
      try {
        // 收集表单数据
        const formData = this.collectFormData();
        if (!formData) {
          return;
        }

        // 设置加载状态
        this.isLoading = true;
        this.$message.success("开始测算...");

        // 调用API
        const response = await maxOutput(formData);

        // 处理响应数据
        if (response && response.code === 200) {
          this.$message.success("测算完成");
          this.isCalculating = true;

          // 这里可以处理返回的结果数据
          console.log("API响应:", response);

          // TODO: 根据实际API响应格式更新resultData
          // 示例：如果API返回了结果数据
          if (response.data) {
            // this.resultData = response.data.results || this.resultData;
          }
        } else {
          this.$message.error(response?.message || "测算失败");
        }
      } catch (error) {
        console.error("API调用失败:", error);

        // 处理不同类型的错误
        if (error.missingFields) {
          this.$message.error(`缺少必需参数: ${error.missingFields.join(', ')}`);
        } else if (error.invalidFields) {
          this.$message.error(`以下字段必须为数值类型: ${error.invalidFields.join(', ')}`);
        } else if (error.response) {
          // HTTP错误
          const status = error.response.status;
          const message = error.response.data?.message || `请求失败 (${status})`;
          this.$message.error(message);
        } else if (error.message) {
          // 其他已知错误
          this.$message.error(error.message);
        } else {
          // 未知错误
          this.$message.error("测算失败，请检查网络连接或联系技术支持");
        }
      } finally {
        // 无论成功还是失败都要清除加载状态
        this.isLoading = false;
      }
    },

    resetCalculation() {
      this.isCalculating = false;
      this.isLoading = false;

      // 清空表单数据
      this.form = {
        productionUnit: "",
        productionTarget: "",
        profitTarget: "",
      };

      // 清空销售渠道数据
      this.salesChannels.forEach(channel => {
        channel.price = "";
        channel.expansionCoefficient = "";
      });

      // 清空供应策略数据
      this.supplyStrategy.forEach(strategy => {
        strategy.minSupply = "";
        strategy.maxReceive = "";
      });

      // 清空气田约束数据
      this.fieldConstraints.forEach(field => {
        field.minProduction = "";
        field.maxProduction = "";
      });

      this.$message.success("表单已重置");
    },

    /**
     * 表单验证辅助方法
     */
    validateForm() {
      const errors = [];

      // 验证基础参数
      if (!this.form.productionUnit?.trim()) errors.push("产量单位");
      if (!this.form.productionTarget) errors.push("计划产量目标");
      if (!this.form.profitTarget) errors.push("利润目标");

      // 验证销售渠道
      this.salesChannels.forEach((channel) => {
        if (!channel.price) errors.push(`${channel.channel}价格`);
        if (!channel.expansionCoefficient) errors.push(`${channel.channel}膨胀系数`);
      });

      // 验证供应策略
      this.supplyStrategy.forEach((strategy) => {
        if (!strategy.minSupply) errors.push(`${strategy.channel}最小供应量`);
        if (!strategy.maxReceive) errors.push(`${strategy.channel}最大接收量`);
      });

      // 验证气田约束
      this.fieldConstraints.forEach((field) => {
        if (!field.minProduction) errors.push(`${field.field}最低产量`);
        if (!field.maxProduction) errors.push(`${field.field}最高产量`);
      });

      return errors;
    },
  },
};
</script>

<style scoped>
.production-sales-model {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-card {
  margin-bottom: 20px;
}

.section-box {
  display: flex;
}

.card-left {
  flex: 1;
  margin-right: 12px;
}

.card-right {
  flex: 1;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
  margin-bottom: 20px;
}
.result-title {
  display: flex;
  align-items: center;
  width: 100%;
  background: #ecf7ff;
  border-radius: 4px;
  box-sizing: border-box;
  border: 1px solid #1677ff;
}
.result-title-text {
  width: 100px;
  height: 26px;
  font-family: Source Han Sans;
  font-size: 14px;
  color: #fff;
  background: #1677ff;
  text-align: center;
}
.result-title-formula {
  color: #1677ff;
  font-family: Source Han Sans;
  font-size: 14px;
  margin-left: 20%;
}

</style>
