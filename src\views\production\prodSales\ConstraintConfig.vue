<template>
  <div class="constraint-config">
    <el-card shadow="hover" class="section-card">
      <div slot="header">
        <h2>约束条件配置</h2>
      </div>
      
      <div class="config-container">
        <!-- 左侧终端列表 -->
        <div class="terminal-section">
          <h3 class="section-title">终端列表</h3>
          <div class="item-list">
            <div 
              v-for="(terminal, index) in terminals" 
              :key="terminal.id"
              :class="['list-item', { 'selected': selectedTerminal && selectedTerminal.id === terminal.id }]"
              @click="selectTerminal(terminal)"
            >
              <div class="item-content">
                <span class="item-name">{{ terminal.name }}</span>
                <div class="connection-point" :data-terminal-id="terminal.id"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间连线区域 -->
        <div class="connection-area" ref="connectionArea">
          <svg class="connection-svg" :width="connectionAreaWidth" :height="connectionAreaHeight">
            <line 
              v-for="connection in connections" 
              :key="`${connection.terminalId}-${connection.channelId}`"
              :x1="connection.x1" 
              :y1="connection.y1" 
              :x2="connection.x2" 
              :y2="connection.y2"
              stroke="#1677ff" 
              stroke-width="2"
              class="connection-line"
            />
          </svg>
          <div class="connection-hint" v-if="!connections.length">
            <span>点击左侧终端和右侧渠道进行连线配置</span>
          </div>
        </div>

        <!-- 右侧渠道列表 -->
        <div class="channel-section">
          <h3 class="section-title">渠道列表</h3>
          <div class="item-list">
            <div 
              v-for="(channel, index) in channels" 
              :key="channel.id"
              :class="['list-item', { 'selected': selectedChannel && selectedChannel.id === channel.id }]"
              @click="selectChannel(channel)"
            >
              <div class="item-content">
                <div class="connection-point" :data-channel-id="channel.id"></div>
                <span class="item-name">{{ channel.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" @click="addConnection" :disabled="!canAddConnection">
          添加连线
        </el-button>
        <el-button @click="clearConnections">
          清空连线
        </el-button>
        <el-button @click="resetSelection">
          重置选择
        </el-button>
      </div>

      <!-- 连线列表 -->
      <div class="connection-list" v-if="connections.length">
        <h3 class="section-title">当前连线配置</h3>
        <el-table :data="connectionTableData" border style="width: 100%">
          <el-table-column type="index" label="序号" width="80"></el-table-column>
          <el-table-column prop="terminalName" label="终端" width="200"></el-table-column>
          <el-table-column prop="channelName" label="渠道" width="200"></el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button 
                type="text" 
                size="small" 
                @click="removeConnection(scope.row.terminalId, scope.row.channelId)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ConstraintConfig',
  data() {
    return {
      // 终端列表数据
      terminals: [
        { id: 1, name: '崖城13-1' },
        { id: 2, name: '崖城13-10' },
        { id: 3, name: '陵水17-2' },
        { id: 4, name: '陵水25-1' }
      ],
      // 渠道列表数据
      channels: [
        { id: 1, name: '香港中电' },
        { id: 2, name: '气电南山' },
        { id: 3, name: '气电广东' }
      ],
      // 当前选中的终端和渠道
      selectedTerminal: null,
      selectedChannel: null,
      // 连线数据
      connections: [],
      // 连线区域尺寸
      connectionAreaWidth: 300,
      connectionAreaHeight: 400
    }
  },
  computed: {
    // 是否可以添加连线
    canAddConnection() {
      if (!this.selectedTerminal || !this.selectedChannel) {
        return false
      }
      // 检查是否已存在相同连线
      return !this.connections.some(conn => 
        conn.terminalId === this.selectedTerminal.id && 
        conn.channelId === this.selectedChannel.id
      )
    },
    // 连线表格数据
    connectionTableData() {
      return this.connections.map(conn => {
        const terminal = this.terminals.find(t => t.id === conn.terminalId)
        const channel = this.channels.find(c => c.id === conn.channelId)
        return {
          terminalId: conn.terminalId,
          channelId: conn.channelId,
          terminalName: terminal ? terminal.name : '',
          channelName: channel ? channel.name : ''
        }
      })
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.updateConnectionArea()
      window.addEventListener('resize', this.updateConnectionArea)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateConnectionArea)
  },
  methods: {
    // 选择终端
    selectTerminal(terminal) {
      this.selectedTerminal = this.selectedTerminal && this.selectedTerminal.id === terminal.id ? null : terminal
    },
    // 选择渠道
    selectChannel(channel) {
      this.selectedChannel = this.selectedChannel && this.selectedChannel.id === channel.id ? null : channel
    },
    // 添加连线
    addConnection() {
      if (!this.canAddConnection) {
        return
      }
      
      const connection = this.calculateConnectionCoordinates(this.selectedTerminal.id, this.selectedChannel.id)
      this.connections.push(connection)
      
      this.$message.success(`已添加 ${this.selectedTerminal.name} 到 ${this.selectedChannel.name} 的连线`)
      this.resetSelection()
    },
    // 删除连线
    removeConnection(terminalId, channelId) {
      const index = this.connections.findIndex(conn => 
        conn.terminalId === terminalId && conn.channelId === channelId
      )
      if (index > -1) {
        this.connections.splice(index, 1)
        this.$message.success('连线已删除')
      }
    },
    // 清空所有连线
    clearConnections() {
      this.$confirm('确认清空所有连线配置？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.connections = []
        this.$message.success('已清空所有连线')
      }).catch(() => {})
    },
    // 重置选择
    resetSelection() {
      this.selectedTerminal = null
      this.selectedChannel = null
    },
    // 计算连线坐标
    calculateConnectionCoordinates(terminalId, channelId) {
      const terminalIndex = this.terminals.findIndex(t => t.id === terminalId)
      const channelIndex = this.channels.findIndex(c => c.id === channelId)
      
      const itemHeight = 60
      const itemSpacing = 10
      const startY = 50
      
      const y1 = startY + terminalIndex * (itemHeight + itemSpacing) + itemHeight / 2
      const y2 = startY + channelIndex * (itemHeight + itemSpacing) + itemHeight / 2
      
      return {
        terminalId,
        channelId,
        x1: 20,
        y1,
        x2: this.connectionAreaWidth - 20,
        y2
      }
    },
    // 更新连线区域
    updateConnectionArea() {
      if (this.$refs.connectionArea) {
        const rect = this.$refs.connectionArea.getBoundingClientRect()
        this.connectionAreaWidth = rect.width
        this.connectionAreaHeight = Math.max(400, this.terminals.length * 70, this.channels.length * 70)
        
        // 重新计算所有连线坐标
        this.connections = this.connections.map(conn => 
          this.calculateConnectionCoordinates(conn.terminalId, conn.channelId)
        )
      }
    }
  }
}
</script>

<style scoped>
.constraint-config {
  width: 100%;
}

.section-card {
  margin-bottom: 20px;
}

.config-container {
  display: flex;
  gap: 20px;
  min-height: 400px;
  position: relative;
}

.terminal-section,
.channel-section {
  flex: 1;
  min-width: 200px;
}

.connection-area {
  flex: 1;
  position: relative;
  min-width: 300px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #1677ff;
}

.item-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.list-item {
  padding: 15px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.list-item:hover {
  border-color: #1677ff;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.15);
}

.list-item.selected {
  border-color: #1677ff;
  background-color: #e6f7ff;
  box-shadow: 0 2px 8px rgba(22, 119, 255, 0.25);
}

.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.item-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.connection-point {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #1677ff;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px #1677ff;
  position: relative;
}

.terminal-section .connection-point {
  margin-left: 10px;
}

.channel-section .connection-point {
  margin-right: 10px;
}

.connection-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-line {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.connection-hint {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 14px;
  text-align: center;
  line-height: 1.5;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.action-buttons .el-button {
  margin: 0 8px;
}

.connection-list {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .config-container {
    flex-direction: column;
  }
  
  .connection-area {
    min-height: 200px;
    order: 3;
  }
  
  .terminal-section {
    order: 1;
  }
  
  .channel-section {
    order: 2;
  }
}
</style>
