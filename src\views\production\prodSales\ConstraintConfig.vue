<template>
  <div class="constraint-config">
    <div class="config-container">
      <!-- 左侧终端列表 -->
      <div class="terminal-section">
        <h4 class="section-title">终端列表</h4>
        <div class="item-list">
          <div
            v-for="(terminal, index) in terminals"
            :key="terminal.id"
            :class="['list-item', { 'active': currentTerminal && currentTerminal.id === terminal.id }]"
            @click="handleTerminalClick(terminal)"
          >
            <div class="item-content">
              <span class="item-name">{{ terminal.name }}</span>
              <div class="connection-point" :data-terminal-id="terminal.id"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间连线区域 -->
      <div class="connection-area" ref="connectionArea">
        <svg class="connection-svg" :width="connectionAreaWidth" :height="connectionAreaHeight">
          <line
            v-for="connection in connections"
            :key="`${connection.terminalId}-${connection.channelId}`"
            :x1="connection.x1"
            :y1="connection.y1"
            :x2="connection.x2"
            :y2="connection.y2"
            stroke="#1677ff"
            stroke-width="2"
            class="connection-line"
          />
        </svg>
        <div class="connection-hint" v-if="!connections.length">
          <span>点击终端，再点击渠道建立连线</span>
        </div>
        <div class="operation-hint" v-if="currentTerminal">
          <span>已选择终端：{{ currentTerminal.name }}，请选择渠道</span>
        </div>
      </div>

      <!-- 右侧渠道列表 -->
      <div class="channel-section">
        <h4 class="section-title">渠道列表</h4>
        <div class="item-list">
          <div
            v-for="(channel, index) in channels"
            :key="channel.id"
            :class="['list-item', { 'connected': isChannelConnected(channel.id) }]"
            @click="handleChannelClick(channel)"
          >
            <div class="item-content">
              <div class="connection-point" :data-channel-id="channel.id"></div>
              <span class="item-name">{{ channel.name }}</span>
              <span class="connection-count" v-if="getChannelConnectionCount(channel.id) > 0">
                ({{ getChannelConnectionCount(channel.id) }})
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 连线列表 -->
    <div class="connection-list" v-if="connections.length">
      <h4 class="section-title">当前连线配置</h4>
      <div class="connection-tags">
        <el-tag
          v-for="connection in connectionTableData"
          :key="`${connection.terminalId}-${connection.channelId}`"
          closable
          @close="removeConnection(connection.terminalId, connection.channelId)"
          class="connection-tag"
        >
          {{ connection.terminalName }} → {{ connection.channelName }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConstraintConfig',
  data() {
    return {
      // 终端列表数据
      terminals: [
        { id: 1, name: '崖城13-1' },
        { id: 2, name: '崖城13-10' },
        { id: 3, name: '陵水17-2' },
        { id: 4, name: '陵水25-1' }
      ],
      // 渠道列表数据
      channels: [
        { id: 1, name: '香港中电' },
        { id: 2, name: '气电南山' },
        { id: 3, name: '气电广东' }
      ],
      // 当前选中的终端（用于两步操作）
      currentTerminal: null,
      // 连线数据
      connections: [],
      // 连线区域尺寸
      connectionAreaWidth: 250,
      connectionAreaHeight: 300
    }
  },
  computed: {
    // 连线表格数据
    connectionTableData() {
      return this.connections.map(conn => {
        const terminal = this.terminals.find(t => t.id === conn.terminalId)
        const channel = this.channels.find(c => c.id === conn.channelId)
        return {
          terminalId: conn.terminalId,
          channelId: conn.channelId,
          terminalName: terminal ? terminal.name : '',
          channelName: channel ? channel.name : ''
        }
      })
    }
  },
  // 移除watch，避免循环调用问题
  mounted() {
    // 简化mounted，避免复杂的DOM操作
    this.connectionAreaWidth = 250
    this.connectionAreaHeight = 300

    // 添加简单的resize监听
    this.debouncedResize = () => {
      this.updateConnectionArea()
    }
    window.addEventListener('resize', this.debouncedResize)
  },
  beforeDestroy() {
    if (this.debouncedResize) {
      window.removeEventListener('resize', this.debouncedResize)
    }
    if (this.resizeTimer) {
      clearTimeout(this.resizeTimer)
    }
  },
  methods: {
    // 处理终端点击
    handleTerminalClick(terminal) {
      this.currentTerminal = terminal
    },
    // 处理渠道点击
    handleChannelClick(channel) {
      if (!this.currentTerminal) {
        this.$message.warning('请先选择一个终端')
        return
      }

      // 检查是否已存在连线
      const existingConnectionIndex = this.connections.findIndex(conn =>
        conn.terminalId === this.currentTerminal.id && conn.channelId === channel.id
      )

      if (existingConnectionIndex > -1) {
        // 如果连线已存在，则删除
        this.connections.splice(existingConnectionIndex, 1)
        this.$message.success(`已取消 ${this.currentTerminal.name} 到 ${channel.name} 的连线`)
      } else {
        // 如果连线不存在，则添加
        const connection = this.calculateConnectionCoordinates(this.currentTerminal.id, channel.id)
        this.connections.push(connection)
        this.$message.success(`已建立 ${this.currentTerminal.name} 到 ${channel.name} 的连线`)
      }

      // 重置当前选择的终端
      this.currentTerminal = null
    },
    // 删除连线
    removeConnection(terminalId, channelId) {
      const index = this.connections.findIndex(conn =>
        conn.terminalId === terminalId && conn.channelId === channelId
      )
      if (index > -1) {
        this.connections.splice(index, 1)
        this.$message.success('连线已删除')
      }
    },
    // 检查渠道是否有连线
    isChannelConnected(channelId) {
      return this.connections.some(conn => conn.channelId === channelId)
    },
    // 获取渠道连线数量
    getChannelConnectionCount(channelId) {
      return this.connections.filter(conn => conn.channelId === channelId).length
    },
    // 计算连线坐标（简化版本）
    calculateConnectionCoordinates(terminalId, channelId) {
      const terminalIndex = this.terminals.findIndex(t => t.id === terminalId)
      const channelIndex = this.channels.findIndex(c => c.id === channelId)

      // 使用固定的布局计算，避免DOM查询导致的问题
      const itemHeight = 50 // 每个项目的高度
      const itemSpacing = 8  // 项目间距
      const titleHeight = 40 // 标题高度
      const startY = titleHeight + 10 // 起始Y位置

      const y1 = startY + terminalIndex * (itemHeight + itemSpacing) + itemHeight / 2
      const y2 = startY + channelIndex * (itemHeight + itemSpacing) + itemHeight / 2

      return {
        terminalId,
        channelId,
        x1: 10, // 左侧起点
        y1,
        x2: this.connectionAreaWidth - 10, // 右侧终点
        y2
      }
    },
    // 更新连线区域
    updateConnectionArea() {
      if (this.$refs.connectionArea) {
        const rect = this.$refs.connectionArea.getBoundingClientRect()
        this.connectionAreaWidth = rect.width || 250
        this.connectionAreaHeight = Math.max(300, this.terminals.length * 60, this.channels.length * 60)

        // 简单地重新计算连线位置
        if (this.connections.length > 0) {
          this.refreshConnectionPositions()
        }
      }
    },

    // 刷新连线位置（用于外部调用）
    refreshConnections() {
      this.updateConnectionArea()
    },

    // 只刷新连线位置，不触发watch
    refreshConnectionPositions() {
      if (this.connections.length > 0) {
        // 直接更新每个连线的坐标，不重新赋值整个数组
        this.connections.forEach((conn, index) => {
          const newCoords = this.calculateConnectionCoordinates(conn.terminalId, conn.channelId)
          this.$set(this.connections, index, {
            ...conn,
            x1: newCoords.x1,
            y1: newCoords.y1,
            x2: newCoords.x2,
            y2: newCoords.y2
          })
        })
      }
    }
  },

  // 组件更新后重新计算连线位置
  updated() {
    // 移除自动更新，避免循环调用
    // 改为手动调用refreshConnections()
  }
}
</script>

<style scoped>
.constraint-config {
  width: 100%;
}

.config-container {
  display: flex;
  gap: 15px;
  min-height: 300px;
  position: relative;
  margin-bottom: 20px;
}

.terminal-section,
.channel-section {
  flex: 1;
  min-width: 150px;
}

.connection-area {
  flex: 1;
  position: relative;
  min-width: 250px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding-bottom: 6px;
  border-bottom: 1px solid #1677ff;
}

.item-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.list-item {
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 8px;
}

.list-item:hover {
  border-color: #1677ff;
  box-shadow: 0 1px 4px rgba(22, 119, 255, 0.15);
}

.list-item.active {
  border-color: #1677ff;
  background-color: #e6f7ff;
  box-shadow: 0 1px 4px rgba(22, 119, 255, 0.25);
}

.list-item.connected {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.item-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.connection-count {
  font-size: 12px;
  color: #52c41a;
  font-weight: 600;
  margin-left: 5px;
}

.connection-point {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #1677ff;
  border: 1px solid #fff;
  box-shadow: 0 0 0 1px #1677ff;
  position: relative;
  flex-shrink: 0; /* 防止连接点被压缩 */
}

.terminal-section .connection-point {
  margin-left: 8px;
}

.channel-section .connection-point {
  margin-right: 8px;
}

/* 确保连接点在连线时更明显 */
.list-item.active .connection-point,
.list-item.connected .connection-point {
  background-color: #52c41a;
  box-shadow: 0 0 0 1px #52c41a;
  transform: scale(1.2);
  transition: all 0.3s ease;
}

.connection-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.connection-line {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: all 0.3s ease;
  stroke-dasharray: 0;
  animation: drawLine 0.5s ease-out;
}

@keyframes drawLine {
  from {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
  }
  to {
    stroke-dasharray: 0;
    stroke-dashoffset: 0;
  }
}

.connection-hint {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #999;
  font-size: 12px;
  text-align: center;
  line-height: 1.5;
}

.operation-hint {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #1677ff;
  font-size: 12px;
  text-align: center;
  line-height: 1.5;
  background: rgba(22, 119, 255, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.connection-list {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.connection-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.connection-tag {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .config-container {
    flex-direction: column;
    gap: 10px;
  }

  .connection-area {
    min-height: 150px;
    order: 3;
  }

  .terminal-section {
    order: 1;
  }

  .channel-section {
    order: 2;
  }
}
</style>
