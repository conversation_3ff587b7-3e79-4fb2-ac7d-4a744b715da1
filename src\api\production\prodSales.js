import { instance } from "../config";

/**
 * 产销配比模型 - 最大产量计算
 * @param {Object} data - 请求参数
 * @param {number} data.f1 - 香港中电膨胀系数
 * @param {number} data.f2 - 气电南山膨胀系数
 * @param {number} data.f3 - 气电广东膨胀系数
 * @param {number} data.m1Max - 香港中电最大接收量
 * @param {number} data.m1Min - 香港中电最小供应量
 * @param {number} data.m2Max - 气电南山最大接收量
 * @param {number} data.m2Min - 气电南山最小供应量
 * @param {number} data.m3Max - 气电广东最大接收量
 * @param {number} data.m3Min - 气电广东最小供应量
 * @param {number} data.maMax - 崖城13-1气田最高产量
 * @param {number} data.maMin - 崖城13-1气田最低产量
 * @param {number} data.mbMax - 崖城13-10气田最高产量
 * @param {number} data.mbMin - 崖城13-10气田最低产量
 * @param {number} data.mcMax - 陵水17-2气田最高产量
 * @param {number} data.mcMin - 陵水17-2气田最低产量
 * @param {number} data.mdMax - 陵水25-1气田最高产量
 * @param {number} data.mdMin - 陵水25-1气田最低产量
 * @param {number} data.ms - 计划产量
 * @param {number} data.p1 - 香港中电价格
 * @param {number} data.p2 - 气电南山价格
 * @param {number} data.p3 - 气电广东价格
 * @param {number} data.ss - 利润目标
 * @param {string} data.unit - 产量单位
 * @returns {Promise} API响应
 */
export function maxOutput(data) {
  // 参数验证
  const requiredFields = [
    'f1', 'f2', 'f3',
    'm1Max', 'm1Min', 'm2Max', 'm2Min', 'm3Max', 'm3Min',
    'maMax', 'maMin', 'mbMax', 'mbMin', 'mcMax', 'mcMin', 'mdMax', 'mdMin',
    'ms', 'p1', 'p2', 'p3', 'ss', 'unit'
  ];

  const missingFields = requiredFields.filter(field =>
    data[field] === undefined || data[field] === null || data[field] === ''
  );

  if (missingFields.length > 0) {
    return Promise.reject({
      message: `缺少必需参数: ${missingFields.join(', ')}`,
      missingFields
    });
  }

  // 数值类型验证
  const numericFields = requiredFields.filter(field => field !== 'unit');
  const invalidNumericFields = numericFields.filter(field =>
    isNaN(Number(data[field]))
  );

  if (invalidNumericFields.length > 0) {
    return Promise.reject({
      message: `以下字段必须为数值类型: ${invalidNumericFields.join(', ')}`,
      invalidFields: invalidNumericFields
    });
  }

  // 转换数值类型
  const processedData = { ...data };
  numericFields.forEach(field => {
    processedData[field] = Number(data[field]);
  });

  return instance({
    url: `/model/maxOutput`,
    method: "post",
    data: processedData,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}