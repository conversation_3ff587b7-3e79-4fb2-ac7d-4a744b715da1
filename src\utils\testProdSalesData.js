/**
 * 产销配比模型测试数据
 * 用于验证前端数据格式化和API调用功能
 */

// 测试数据集
export const testFormData = {
  // 基础参数
  form: {
    productionUnit: "万立方米/天",
    productionTarget: "1000",
    profitTarget: "500000"
  },

  // 销售渠道数据
  salesChannels: [
    { id: 1, channel: "香港中电", price: "2.5", expansionCoefficient: "1.05" },
    { id: 2, channel: "气电南山", price: "2.8", expansionCoefficient: "1.08" },
    { id: 3, channel: "气电广东", price: "3.0", expansionCoefficient: "1.10" }
  ],

  // 供应策略数据
  supplyStrategy: [
    { id: 1, channel: "香港中电", minSupply: "100", maxReceive: "500" },
    { id: 2, channel: "气电南山", minSupply: "150", maxReceive: "600" },
    { id: 3, channel: "气电广东", minSupply: "200", maxReceive: "700" }
  ],

  // 气田约束数据
  fieldConstraints: [
    { id: 1, field: "崖城13-1", minProduction: "50", maxProduction: "300" },
    { id: 2, field: "崖城13-10", minProduction: "80", maxProduction: "400" },
    { id: 3, field: "陵水17-2", minProduction: "100", maxProduction: "500" },
    { id: 4, field: "陵水25-1", minProduction: "120", maxProduction: "600" }
  ]
};

// 期望的API数据格式
export const expectedApiData = {
  // 膨胀系数参数
  f1: 1.05, // 香港中电
  f2: 1.08, // 气电南山
  f3: 1.10, // 气电广东

  // 价格参数
  p1: 2.5, // 香港中电
  p2: 2.8, // 气电南山
  p3: 3.0, // 气电广东

  // 接收量/供应量参数
  m1Max: 500, // 香港中电最大接收量
  m1Min: 100, // 香港中电最小供应量
  m2Max: 600, // 气电南山最大接收量
  m2Min: 150, // 气电南山最小供应量
  m3Max: 700, // 气电广东最大接收量
  m3Min: 200, // 气电广东最小供应量

  // 气田产量参数
  maMax: 300, // 崖城13-1最高产量
  maMin: 50,  // 崖城13-1最低产量
  mbMax: 400, // 崖城13-10最高产量
  mbMin: 80,  // 崖城13-10最低产量
  mcMax: 500, // 陵水17-2最高产量
  mcMin: 100, // 陵水17-2最低产量
  mdMax: 600, // 陵水25-1最高产量
  mdMin: 120, // 陵水25-1最低产量

  // 其他参数
  ms: 1000,              // 计划产量
  ss: 500000,            // 利润目标
  unit: "万立方米/天"     // 产量单位
};

/**
 * 验证数据格式化是否正确
 * @param {Object} formattedData - 格式化后的数据
 * @returns {Object} 验证结果
 */
export function validateDataFormat(formattedData) {
  const errors = [];
  const warnings = [];

  // 检查必需字段
  const requiredFields = [
    'f1', 'f2', 'f3',
    'm1Max', 'm1Min', 'm2Max', 'm2Min', 'm3Max', 'm3Min',
    'maMax', 'maMin', 'mbMax', 'mbMin', 'mcMax', 'mcMin', 'mdMax', 'mdMin',
    'ms', 'p1', 'p2', 'p3', 'ss', 'unit'
  ];

  requiredFields.forEach(field => {
    if (!(field in formattedData)) {
      errors.push(`缺少字段: ${field}`);
    } else if (field !== 'unit' && typeof formattedData[field] !== 'number') {
      errors.push(`字段 ${field} 应为数值类型，当前为: ${typeof formattedData[field]}`);
    }
  });

  // 检查数值范围
  const numericFields = requiredFields.filter(field => field !== 'unit');
  numericFields.forEach(field => {
    if (formattedData[field] < 0) {
      warnings.push(`字段 ${field} 为负数: ${formattedData[field]}`);
    }
  });

  // 检查逻辑关系
  if (formattedData.m1Min > formattedData.m1Max) {
    warnings.push('香港中电最小供应量大于最大接收量');
  }
  if (formattedData.m2Min > formattedData.m2Max) {
    warnings.push('气电南山最小供应量大于最大接收量');
  }
  if (formattedData.m3Min > formattedData.m3Max) {
    warnings.push('气电广东最小供应量大于最大接收量');
  }

  if (formattedData.maMin > formattedData.maMax) {
    warnings.push('崖城13-1最低产量大于最高产量');
  }
  if (formattedData.mbMin > formattedData.mbMax) {
    warnings.push('崖城13-10最低产量大于最高产量');
  }
  if (formattedData.mcMin > formattedData.mcMax) {
    warnings.push('陵水17-2最低产量大于最高产量');
  }
  if (formattedData.mdMin > formattedData.mdMax) {
    warnings.push('陵水25-1最低产量大于最高产量');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary: `验证完成: ${errors.length} 个错误, ${warnings.length} 个警告`
  };
}

/**
 * 打印数据对比结果
 * @param {Object} actual - 实际数据
 * @param {Object} expected - 期望数据
 */
export function compareData(actual, expected) {
  console.log('=== 数据格式化对比结果 ===');
  
  Object.keys(expected).forEach(key => {
    const actualValue = actual[key];
    const expectedValue = expected[key];
    
    if (actualValue === expectedValue) {
      console.log(`✓ ${key}: ${actualValue}`);
    } else {
      console.log(`✗ ${key}: 期望 ${expectedValue}, 实际 ${actualValue}`);
    }
  });
}
